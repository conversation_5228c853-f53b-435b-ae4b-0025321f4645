import{c as U,j as e,n as w,E as S,i as L,o as B,m as A,r as v,F as P,B as k,A as $,G as z,J as H,b as D,K as F,q as M,L as O,t as R,C as q,D as V,s as Y,M as W}from"./index-b7892492.js";const T=U((s,h)=>({balance:0,transactions:[],operationCosts:[],stats:null,isLoading:!1,error:null,fetchBalance:async()=>{s({isLoading:!0,error:null});try{const i=await fetch("/api/credits/balance",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!i.ok)throw new Error("Failed to fetch credit balance");const t=await i.json();if(t.success)s({balance:t.data.credits,isLoading:!1});else throw new Error(t.error||"Failed to fetch balance")}catch(i){s({error:i instanceof Error?i.message:"Unknown error",isLoading:!1})}},fetchTransactions:async(i=50,t=0)=>{s({isLoading:!0,error:null});try{const n=await fetch(`/api/credits/history?limit=${i}&offset=${t}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!n.ok)throw new Error("Failed to fetch credit history");const p=await n.json();if(p.success)s({transactions:t===0?p.data:[...h().transactions,...p.data],isLoading:!1});else throw new Error(p.error||"Failed to fetch transactions")}catch(n){s({error:n instanceof Error?n.message:"Unknown error",isLoading:!1})}},fetchOperationCosts:async()=>{s({isLoading:!0,error:null});try{const i=await fetch("/api/credits/pricing",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!i.ok)throw new Error("Failed to fetch operation costs");const t=await i.json();if(t.success)s({operationCosts:t.data,isLoading:!1});else throw new Error(t.error||"Failed to fetch operation costs")}catch(i){s({error:i instanceof Error?i.message:"Unknown error",isLoading:!1})}},fetchStats:async(i=30)=>{s({isLoading:!0,error:null});try{const t=await fetch(`/api/credits/stats?days=${i}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!t.ok)throw new Error("Failed to fetch credit stats");const n=await t.json();if(n.success)s({stats:n.data,isLoading:!1});else throw new Error(n.error||"Failed to fetch stats")}catch(t){s({error:t instanceof Error?t.message:"Unknown error",isLoading:!1})}},purchaseCredits:async i=>{s({isLoading:!0,error:null});try{const t=await fetch("/api/credits/purchase",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`},body:JSON.stringify(i)});if(!t.ok)throw new Error("Failed to initiate credit purchase");const n=await t.json();if(n.success)return s({isLoading:!1}),{success:!0,clientSecret:n.data.clientSecret,paymentIntentId:n.data.paymentIntentId};throw new Error(n.error||"Failed to purchase credits")}catch(t){const n=t instanceof Error?t.message:"Unknown error";return s({error:n,isLoading:!1}),{success:!1,error:n}}},clearError:()=>s({error:null}),refreshAfterPurchase:async()=>{try{await Promise.all([h().fetchBalance(),h().fetchTransactions()])}catch(i){console.error("Failed to refresh data after purchase:",i)}}})),G=({balance:s,userTier:h,isLoading:i})=>{const t=l=>{switch(l.toLowerCase()){case"pro":return"text-purple-400";case"basic":return"text-blue-400";default:return"text-gray-400"}},n=l=>{switch(l.toLowerCase()){case"pro":return e.jsx(A,{className:"w-5 h-5"});case"basic":return e.jsx(B,{className:"w-5 h-5"});default:return e.jsx(S,{className:"w-5 h-5"})}},o=(l=>l>=100?{color:"text-green-400",status:"Excellent"}:l>=50?{color:"text-yellow-400",status:"Good"}:l>=10?{color:"text-orange-400",status:"Low"}:{color:"text-red-400",status:"Critical"})(s);return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:e.jsx(S,{className:"w-6 h-6 text-primary-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Credit Balance"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Available for AI generation"})]})]}),e.jsxs("div",{className:`flex items-center space-x-2 ${t(h)}`,children:[n(h),e.jsxs("span",{className:"font-medium",children:[h," Plan"]})]})]}),e.jsxs("div",{className:"flex items-end space-x-4",children:[e.jsx("div",{children:i?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-12 w-32 bg-gray-600 rounded"})}):e.jsxs(w.div,{initial:{scale:.8},animate:{scale:1},transition:{duration:.3,delay:.2},children:[e.jsx("span",{className:"text-4xl font-bold text-white",children:s.toLocaleString()}),e.jsx("span",{className:"text-xl text-gray-400 ml-2",children:"credits"})]})}),e.jsxs("div",{className:`flex items-center space-x-1 ${o.color} mb-2`,children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${o.color.replace("text-","bg-")}`}),e.jsx("span",{className:"text-sm font-medium",children:o.status})]})]}),s<10&&e.jsxs(w.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm font-medium",children:"Low Balance Warning"})]}),e.jsx("p",{className:"text-red-300 text-sm mt-1",children:"Consider purchasing more credits to continue using AI features."})]})]}),e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Plan Type"}),e.jsx("span",{className:`font-medium ${t(h)}`,children:h})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Status"}),e.jsx("span",{className:`font-medium ${o.color}`,children:o.status})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Credits Available"}),e.jsx("span",{className:"text-white font-medium",children:s})]}),h.toLowerCase()==="free"&&e.jsx("div",{className:"pt-3 border-t border-border-secondary",children:e.jsx("p",{className:"text-gray-400 text-xs",children:"Upgrade to Basic or Pro for more credits and features"})})]})]})]})},J=({transactions:s,isLoading:h,onLoadMore:i})=>{const[t,n]=v.useState("all"),[p,o]=v.useState("date"),l=r=>r>0?e.jsx(z,{className:"w-4 h-4 text-red-400"}):e.jsx(H,{className:"w-4 h-4 text-green-400"}),y=r=>r>0?"text-red-400":"text-green-400",j=r=>{const f=new Date(r);return{date:f.toLocaleDateString(),time:f.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},c=s.filter(r=>t==="used"?r.credits_used>0:t==="purchased"?r.credits_used<0:!0),m=[...c].sort((r,f)=>p==="date"?new Date(f.created_at).getTime()-new Date(r.created_at).getTime():Math.abs(f.credits_used)-Math.abs(r.credits_used)),d=()=>{const r=[["Date","Type","Credits","Operation","Description"].join(","),...m.map(u=>[new Date(u.created_at).toLocaleDateString(),u.credits_used>0?"Used":"Purchased",Math.abs(u.credits_used),u.operation_type,`"${u.description}"`].join(","))].join(`
`),f=new Blob([r],{type:"text/csv"}),b=window.URL.createObjectURL(f),a=document.createElement("a");a.href=b,a.download=`credit-history-${new Date().toISOString().split("T")[0]}.csv`,a.click(),window.URL.revokeObjectURL(b)};return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Transaction History"}),e.jsxs("p",{className:"text-gray-400 text-sm",children:[c.length," of ",s.length," transactions"]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:t,onChange:r=>n(r.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"all",children:"All Transactions"}),e.jsx("option",{value:"used",children:"Credits Used"}),e.jsx("option",{value:"purchased",children:"Credits Purchased"})]}),e.jsx(P,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:p,onChange:r=>o(r.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"date",children:"Sort by Date"}),e.jsx("option",{value:"amount",children:"Sort by Amount"})]}),e.jsx(P,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs(k,{onClick:d,variant:"secondary",size:"sm",disabled:s.length===0,children:[e.jsx($,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),e.jsx("div",{className:"space-y-3",children:h&&s.length===0?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((r,f)=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-600 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-600 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-600 rounded w-1/2"})]}),e.jsx("div",{className:"h-6 bg-gray-600 rounded w-16"})]})})},f))}):m.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(L,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-400 mb-2",children:"No Transactions Found"}),e.jsx("p",{className:"text-gray-500",children:t==="all"?"You haven't made any credit transactions yet.":`No ${t} transactions found.`})]}):m.map((r,f)=>{const{date:b,time:a}=j(r.created_at),u=r.credits_used<0;return e.jsx(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:f*.05},className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`p-2 rounded-full ${u?"bg-green-500/20":"bg-red-500/20"}`,children:l(r.credits_used)}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:r.operation_type.replace(/_/g," ").replace(/\b\w/g,g=>g.toUpperCase())}),e.jsx("p",{className:"text-gray-400 text-sm",children:r.description}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("span",{className:"text-gray-500 text-xs",children:b}),e.jsx("span",{className:"text-gray-600",children:"•"}),e.jsx("span",{className:"text-gray-500 text-xs",children:a})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:`font-semibold ${y(r.credits_used)}`,children:[u?"+":"-",Math.abs(r.credits_used)," credits"]}),r.study_set_id&&e.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Study Set"})]})]})},r.id)})}),s.length>0&&s.length%50===0&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx(k,{onClick:i,variant:"secondary",isLoading:h,disabled:h,children:"Load More Transactions"})})]})},_=[{id:"study-buddy",name:"Study Buddy",credits:100,price:9.99,description:"Perfect for high school & early college students",features:["100 study credits (500 flashcards/quizzes)","Covers 2-3 classes per semester","Valid for 6 months","All AI study tools included","Perfect for finals prep"]},{id:"deans-list",name:"Dean's List",credits:500,price:39.99,bonus:50,popular:!0,description:"Most popular for serious undergrads & grad students",features:["500 study credits (2,500 flashcards/quizzes)","+50 bonus credits (250 extra generations)","Covers full semester workload","Valid for 12 months","Priority support for crunch time","All AI study tools included"]},{id:"phd-power",name:"PhD Power",credits:1e3,price:69.99,bonus:200,description:"Built for graduate students & research powerhouses",features:["1,000 study credits (5,000 flashcards/quizzes)","+200 bonus credits (1,000 extra generations)","Handles multiple courses + thesis prep","Valid for 12 months","Priority support","Early access to new AI features","All study tools included"]},{id:"academic-legend",name:"Academic Legend",credits:2500,price:149.99,bonus:750,description:"Ultimate package for study groups & academic overachievers",features:["2,500 study credits (125,000 flashcards/quizzes)","+750 bonus credits (37,500 extra generations)","Perfect for study groups & tutoring","Valid for 18 months","Dedicated academic support","Early access to new features","Custom study integrations","All AI study tools included"]}],K=({currentBalance:s,onPurchaseComplete:h})=>{const[i,t]=v.useState(null),[n,p]=v.useState(!1),[o,l]=v.useState(null),[y,j]=v.useState(null),{purchaseCredits:c,refreshAfterPurchase:m}=T(),{user:d}=D(),r=async a=>{const u=_.find(g=>g.id===a);if(!u){l("Selected package not found. Please try again.");return}if(!(d!=null&&d.email)){l("User email not available. Please log out and log back in.");return}p(!0),t(a),l(null),j(null);try{const g=u.credits+(u.bonus||0),x=await c({amount:g,credits:g,price:u.price,email:d.email,name:d.name});x.success&&x.clientSecret?(console.log("Payment intent created:",x.paymentIntentId),j(`Payment intent created successfully for ${u.name}! Payment processing will be implemented in the next phase.`),l(null),await m(),h()):l(x.error||"Failed to initiate credit purchase. Please try again.")}catch(g){console.error("Purchase error:",g),g instanceof Error?g.message.includes("fetch")?l("Network error. Please check your connection and try again."):g.message.includes("401")||g.message.includes("unauthorized")?l("Authentication error. Please log out and log back in."):l(g.message):l("An unexpected error occurred. Please try again.")}finally{p(!1),t(null)}},f=a=>{switch(a){case"starter":return e.jsx(S,{className:"w-6 h-6"});case"popular":return e.jsx(O,{className:"w-6 h-6"});case"power":return e.jsx(M,{className:"w-6 h-6"});case"enterprise":return e.jsx(A,{className:"w-6 h-6"});default:return e.jsx(S,{className:"w-6 h-6"})}},b=(a,u,g)=>{if(!u)return null;const N=(a+u)/a*g,C=(N-g)/N*100;return Math.round(C)};return e.jsxs("div",{className:"space-y-6",children:[o&&e.jsx("div",{className:"bg-red-900/20 border border-red-500/50 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-400",children:"Error"}),e.jsx("div",{className:"mt-1 text-sm text-red-300",children:o})]}),e.jsx("div",{className:"ml-auto pl-3",children:e.jsxs("button",{onClick:()=>l(null),className:"inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900",children:[e.jsx("span",{className:"sr-only",children:"Dismiss"}),e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),y&&e.jsx("div",{className:"bg-green-900/20 border border-green-500/50 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-green-400",children:"Success"}),e.jsx("div",{className:"mt-1 text-sm text-green-300",children:y})]}),e.jsx("div",{className:"ml-auto pl-3",children:e.jsxs("button",{onClick:()=>j(null),className:"inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900",children:[e.jsx("span",{className:"sr-only",children:"Dismiss"}),e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Current Balance"}),e.jsx("p",{className:"text-gray-400",children:"Your available credits"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:"text-2xl font-bold text-primary-400",children:s}),e.jsx("span",{className:"text-gray-400 ml-2",children:"credits"})]})]})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Power Up Your Studies"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Choose the perfect study package for your academic goals"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:_.map((a,u)=>{const g=a.credits+(a.bonus||0),x=a.bonus?b(a.credits,a.bonus,a.price):null,N=i===a.id,C=n&&N;return e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:u*.1},className:`
                  relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                  ${a.popular?"border-primary-500 ring-2 ring-primary-500/20":"border-border-primary hover:border-border-secondary"}
                  ${N?"ring-2 ring-primary-500/50":""}
                `,children:[a.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsx("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Most Popular"})}),x&&e.jsx("div",{className:"absolute -top-2 -right-2",children:e.jsxs("div",{className:"bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:[x,"% OFF"]})}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`inline-flex p-3 rounded-lg mb-4 ${a.popular?"bg-primary-500/20 text-primary-400":"bg-background-tertiary text-gray-400"}`,children:f(a.id)}),e.jsx("h4",{className:"text-lg font-semibold text-white mb-2",children:a.name}),e.jsxs("div",{className:"mb-4",children:[e.jsx("span",{className:"text-3xl font-bold text-white",children:a.credits}),a.bonus&&e.jsxs("span",{className:"text-green-400 text-sm ml-1",children:["+",a.bonus]}),e.jsx("div",{className:"text-gray-400 text-sm",children:"credits"}),a.bonus&&e.jsxs("div",{className:"text-green-400 text-xs",children:["Total: ",g," credits"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("span",{className:"text-2xl font-bold text-white",children:["$",a.price]}),e.jsxs("div",{className:"text-gray-400 text-sm",children:["$",(a.price/g).toFixed(3)," per credit"]})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:a.description}),e.jsx("div",{className:"space-y-2 mb-6",children:a.features.map((E,I)=>e.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[e.jsx(F,{className:"w-4 h-4 text-green-400 mr-2 flex-shrink-0"}),e.jsx("span",{children:E})]},I))}),e.jsx(k,{onClick:()=>r(a.id),variant:a.popular?"primary":"secondary",className:"w-full",isLoading:C,disabled:n||!(d!=null&&d.email),children:C?"Processing...":d!=null&&d.email?`Purchase ${a.name}`:"Login Required"})]})]},a.id)})})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Student-Friendly Payment"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Safe & Secure"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Student-safe payments through Stripe. Your payment info is never stored. Perfect for using your student card or parent's card with permission."})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Flexible Validity"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Credits last for the full duration shown - perfect for semester planning. No rush to use them all at once!"})]})]})]})]})},Q=({stats:s,operationCosts:h})=>{const i=v.useMemo(()=>{const c=Array.from({length:7},(m,d)=>{const r=new Date;return r.setDate(r.getDate()-(6-d)),{date:r.toLocaleDateString("en-US",{weekday:"short"}),fullDate:r.toISOString().split("T")[0],credits:0}});return s!=null&&s.dailyUsage&&Array.isArray(s.dailyUsage)&&s.dailyUsage.forEach(m=>{const d=c.findIndex(r=>r.fullDate===m.date);d!==-1&&(c[d].credits=m.credits)}),c},[s==null?void 0:s.dailyUsage]),t=Math.max(...i.map(c=>c.credits),1),n=i.reduce((c,m)=>c+m.credits,0),p=n/7,o=i.slice(0,3).reduce((c,m)=>c+m.credits,0)/3,l=i.slice(4).reduce((c,m)=>c+m.credits,0)/3,y=l>o?"up":l<o?"down":"stable",j=o>0?Math.abs((l-o)/o*100):0;return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Usage Trends"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Last 7 days"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[y==="up"?e.jsx(B,{className:"w-5 h-5 text-green-400"}):y==="down"?e.jsx(R,{className:"w-5 h-5 text-red-400"}):null,y!=="stable"&&e.jsxs("span",{className:`text-sm font-medium ${y==="up"?"text-green-400":"text-red-400"}`,children:[j.toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:i.map((c,m)=>{const d=t>0?c.credits/t*100:0;return e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full flex justify-center mb-2",children:e.jsx(w.div,{initial:{height:0},animate:{height:`${d}%`},transition:{duration:.5,delay:m*.1},className:"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group",style:{minHeight:d>0?"4px":"0px"},children:e.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsxs("div",{className:"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap",children:[c.credits," credits"]})})})}),e.jsx("span",{className:"text-xs text-gray-400",children:c.date})]},c.date)})})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:n}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Total This Week"})]})}),e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:p.toFixed(1)}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Daily Average"})]})})]}),e.jsxs("div",{className:"mt-4 pt-4 border-t border-border-secondary",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Usage Efficiency"}),e.jsx("div",{className:"space-y-2",children:s!=null&&s.usageByOperation?Object.entries(s.usageByOperation).slice(0,3).map(([c,m])=>{const d=h==null?void 0:h.find(f=>f.operation_type===c),r=d?m*d.operations_per_credit:0;return e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:c.replace(/_/g," ")}),e.jsxs("span",{className:"text-white",children:[r," generations"]})]},c)}):e.jsx("div",{className:"text-gray-400 text-sm text-center py-2",children:"No usage data available"})})]})]})},X=[{id:"overview",label:"Overview",icon:Y,description:"Credit balance and usage summary"},{id:"history",label:"Transaction History",icon:L,description:"Detailed credit transaction log"},{id:"purchase",label:"Buy Credits",icon:W,description:"Purchase additional credits"}],ee=()=>{const[s,h]=v.useState("overview"),{user:i}=D(),{balance:t,transactions:n,operationCosts:p,stats:o,isLoading:l,error:y,fetchBalance:j,fetchTransactions:c,fetchOperationCosts:m,fetchStats:d,clearError:r}=T();v.useEffect(()=>{j(),c(),m(),d()},[j,c,m,d]);const f=async()=>{r(),await Promise.all([j(),c(),m(),d()])},b=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx(G,{balance:t,userTier:(i==null?void 0:i.subscription_tier)||"Free",isLoading:l}),o&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(Q,{stats:o,operationCosts:p}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Usage Breakdown"}),e.jsx("div",{className:"space-y-3",children:o!=null&&o.usageByOperation?Object.entries(o.usageByOperation).map(([x,N])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:x.replace(/_/g," ")}),e.jsxs("span",{className:"text-white font-medium",children:[N," credits"]})]},x)):e.jsx("div",{className:"text-gray-400 text-center py-4",children:"No usage data available"})})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Credit Costs"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(p==null?void 0:p.length)>0?p.map(x=>e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary",children:[e.jsx("h4",{className:"font-medium text-white capitalize mb-2",children:x.operation_type.replace(/_/g," ")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{className:"w-4 h-4 text-primary-400"}),e.jsxs("span",{className:"text-primary-400 font-semibold",children:["1 credit = ",x.operations_per_credit," ",x.operation_type.replace(/_/g," ").replace("generation","").trim(),"s"]})]})]},x.operation_type)):e.jsx("div",{className:"col-span-full text-gray-400 text-center py-8",children:"No operation cost data available"})})]})]}),a=()=>e.jsx(J,{transactions:n,isLoading:l,onLoadMore:()=>c(50,n.length)}),u=()=>e.jsx(K,{currentBalance:t,userTier:(i==null?void 0:i.subscription_tier)||"Study Starter",onPurchaseComplete:f}),g=()=>{switch(s){case"overview":return b();case"history":return a();case"purchase":return u();default:return b()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Credits"}),e.jsx("p",{className:"text-gray-400",children:"Manage your AI generation credits"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs(k,{onClick:f,variant:"secondary",disabled:l,children:[e.jsx(q,{className:`w-4 h-4 mr-2 ${l?"animate-spin":""}`}),"Refresh"]})})]}),y&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:y}),e.jsx(k,{onClick:r,variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:X.map(x=>{const N=x.icon,C=s===x.id;return e.jsxs("button",{onClick:()=>h(x.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${C?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(N,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:x.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:x.description})]})]},x.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(w.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:g()},s)})]})]})})};export{ee as CreditsPage};
